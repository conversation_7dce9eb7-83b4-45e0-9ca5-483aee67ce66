******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Jun 21 01:50:23 2025

OUTPUT FILE NAME:   <uart_rx_dma_read_jy901s_data.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000032d1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000040f0  0001bf10  R  X
  SRAM                  20200000   00008000  000006a4  0000795c  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000040f0   000040f0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000033e0   000033e0    r-x .text
  000034a0    000034a0    00000c20   00000c20    r-- .rodata
  000040c0    000040c0    00000030   00000030    r-- .cinit
20200000    20200000    000004a4   00000000    rw-
  20200000    20200000    000004a0   00000000    rw- .bss
  202004a0    202004a0    00000004   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000033e0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    000003dc     main.o (.text.main)
                  00000e6c    0000039c     OLED.o (.text.OLED_WriteData)
                  00001208    00000378     OLED.o (.text.OLED_WriteCommand)
                  00001580    00000264     OLED.o (.text.OLED_ShowImage)
                  000017e4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001a04    000001f0     OLED.o (.text.OLED_ShowString)
                  00001bf4    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001dd0    000001bc     OLED.o (.text.OLED_Init)
                  00001f8c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000211e    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00002120    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000225c    00000120            : _printfi.c.obj (.text._pconv_e)
                  0000237c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002488    00000104     OLED.o (.text.OLED_Update)
                  0000258c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002670    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002748    000000a8     delay.o (.text.delay_ms)
                  000027f0    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002892    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000292c    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00002930    00000088     libc.a : strcmp-armv6m.S.obj (.text:strcmp)
                  000029b8    0000007c            : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002a34    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002aa8    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00002ab0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002b24    0000006c     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_JY901S_init)
                  00002b90    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002bf8    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002c5e    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002c60    00000064     OLED.o (.text.OLED_ClearArea)
                  00002cc4    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002d26    00000062     libc.a : memset16.S.obj (.text:memset)
                  00002d88    0000005c            : s_frexp.c.obj (.text.frexp)
                  00002de4    00000058            : _ltoa.c.obj (.text.__TI_ltoa)
                  00002e3c    00000058            : _printfi.c.obj (.text._pconv_f)
                  00002e94    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00002eea    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00002f3c    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00002f88    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002fd2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002fd4    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000301c    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000305c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000309c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000030dc    00000040     libc.a : atoi.c.obj (.text.atoi)
                  0000311c    0000003c            : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003158    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00003192    00000002     --HOLE-- [fill = 0]
                  00003194    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000031c8    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_UART_JY901S_init)
                  000031f8    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003228    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003254    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003280    00000028     OLED.o (.text.OLED_Printf)
                  000032a8    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000032d0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000032f8    00000024     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000331c    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00003340    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003362    00000002     --HOLE-- [fill = 0]
                  00003364    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003382    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  0000339a    00000002     --HOLE-- [fill = 0]
                  0000339c    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000033b0    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000033c4    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000033d6    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  000033e8    00000010            : wcslen.c.obj (.text.wcslen)
                  000033f8    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003408    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00003416    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00003424    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003430    0000000c     delay.o (.text.delay_init)
                  0000343c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003446    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003450    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00003460    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000346a    0000000a            : vsprintf.c.obj (.text._outc)
                  00003474    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0000347c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003484    00000006     libc.a : exit.c.obj (.text:abort)
                  0000348a    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000348e    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00003492    0000000e     --HOLE-- [fill = 0]

.cinit     0    000040c0    00000030     
                  000040c0    0000000c     (__TI_handler_table)
                  000040cc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000040d4    00000007     (.cinit..data.load) [load image, compression = lzss]
                  000040db    00000001     --HOLE-- [fill = 0]
                  000040dc    00000010     (__TI_cinit_table)
                  000040ec    00000004     --HOLE-- [fill = 0]

.rodata    0    000034a0    00000c20     
                  000034a0    000005f0     OLED_Data.o (.rodata.OLED_F8x16)
                  00003a90    0000023a     OLED_Data.o (.rodata.OLED_F6x8)
                  00003cca    0000022b     OLED_Data.o (.rodata.OLED_CF16x16)
                  00003ef5    00000001     --HOLE-- [fill = 0]
                  00003ef6    0000000a     ti_msp_dl_config.o (.rodata.gUART_JY901SConfig)
                  00003f00    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00004001    00000002     ti_msp_dl_config.o (.rodata.gUART_JY901SClockConfig)
                  00004003    00000001     --HOLE-- [fill = 0]
                  00004004    00000018     ti_msp_dl_config.o (.rodata.gDMA_UART_JY901SConfig)
                  0000401c    00000016     main.o (.rodata.str1.15159059442110792349.1)
                  00004032    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00004043    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00004054    00000010     main.o (.rodata.str1.8154729771448623357.1)
                  00004064    0000000e     main.o (.rodata.str1.12180500075719773345.1)
                  00004072    0000000e     main.o (.rodata.str1.14112174005742061988.1)
                  00004080    0000000e     main.o (.rodata.str1.17100691992556644108.1)
                  0000408e    0000000e     main.o (.rodata.str1.18227636981041470289.1)
                  0000409c    0000000e     main.o (.rodata.str1.2846389346932560359.1)
                  000040aa    0000000e     main.o (.rodata.str1.288608917962107522.1)
                  000040b8    00000008     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000004a0     UNINITIALIZED
                  20200000    00000400     (.common:OLED_DisplayBuf)
                  20200400    00000042     (.common:rxData)
                  20200442    00000002     --HOLE--
                  20200444    00000030     (.common:gUART_JY901SBackup)
                  20200474    00000004     (.common:accX)
                  20200478    00000008     (.common:SYSTEMCLOCK)
                  20200480    00000004     (.common:accY)
                  20200484    00000004     (.common:accZ)
                  20200488    00000004     (.common:angleX)
                  2020048c    00000004     (.common:angleY)
                  20200490    00000004     (.common:angleZ)
                  20200494    00000004     (.common:gyroX)
                  20200498    00000004     (.common:gyroY)
                  2020049c    00000004     (.common:gyroZ)

.data      0    202004a0    00000004     UNINITIALIZED
                  202004a0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       main.o                         988     122       102    
       ti_msp_dl_config.o             356     36        48     
       startup_mspm0g350x_ticlang.o   6       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1350    350       150    
                                                               
    .\hardware\delay\
       delay.o                        180     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         180     0         8      
                                                               
    .\hardware\oled\
       OLED.o                         3764    0         1024   
       OLED_Data.o                    0       2645      0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3764    2645      1024   
                                                               
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         176     0         0      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       memcpy16.S.obj                 154     0         0      
       strcmp-armv6m.S.obj            136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5762    291       4      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       udivmoddi4.S.obj               162     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       muldsi3.S.obj                  58      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2024    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       43        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   13260   3329      1698   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000040dc records: 2, size/record: 8, table size: 16
	.bss: load addr=000040cc, load size=00000008 bytes, run addr=20200000, run size=000004a0 bytes, compression=zero_init
	.data: load addr=000040d4, load size=00000007 bytes, run addr=202004a0, run size=00000004 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000040c0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001f8d     00003450     0000344e   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000211f  ADC0_IRQHandler               
0000211f  ADC1_IRQHandler               
0000211f  AES_IRQHandler                
0000292c  C$$EXIT                       
0000211f  CANFD0_IRQHandler             
0000211f  DAC0_IRQHandler               
0000343d  DL_Common_delayCycles         
00002f3d  DL_DMA_initChannel            
00002fd5  DL_UART_init                  
000033c5  DL_UART_setClockConfig        
0000211f  DMA_IRQHandler                
0000211f  Default_Handler               
0000211f  GROUP0_IRQHandler             
0000211f  GROUP1_IRQHandler             
0000292d  HOSTexit                      
0000211f  HardFault_Handler             
0000211f  I2C0_IRQHandler               
0000211f  I2C1_IRQHandler               
0000211f  NMI_Handler                   
00003cca  OLED_CF16x16                  
00002c61  OLED_ClearArea                
20200000  OLED_DisplayBuf               
00003a90  OLED_F6x8                     
000034a0  OLED_F8x16                    
00001dd1  OLED_Init                     
00003281  OLED_Printf                   
00001581  OLED_ShowImage                
00001a05  OLED_ShowString               
00002489  OLED_Update                   
00001209  OLED_WriteCommand             
00000e6d  OLED_WriteData                
0000211f  PendSV_Handler                
0000211f  RTC_IRQHandler                
0000348b  Reset_Handler                 
0000211f  SPI0_IRQHandler               
0000211f  SPI1_IRQHandler               
0000211f  SVC_Handler                   
000031c9  SYSCFG_DL_DMA_UART_JY901S_init
00002aa9  SYSCFG_DL_DMA_init            
000032a9  SYSCFG_DL_GPIO_init           
0000301d  SYSCFG_DL_SYSCTL_init         
00002b25  SYSCFG_DL_UART_JY901S_init    
000032f9  SYSCFG_DL_init                
00003195  SYSCFG_DL_initPower           
20200478  SYSTEMCLOCK                   
0000211f  SysTick_Handler               
0000211f  TIMA0_IRQHandler              
0000211f  TIMA1_IRQHandler              
0000211f  TIMG0_IRQHandler              
0000211f  TIMG12_IRQHandler             
0000211f  TIMG6_IRQHandler              
0000211f  TIMG7_IRQHandler              
0000211f  TIMG8_IRQHandler              
0000211f  UART0_IRQHandler              
0000211f  UART1_IRQHandler              
0000211f  UART2_IRQHandler              
0000211f  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
000040dc  __TI_CINIT_Base               
000040ec  __TI_CINIT_Limit              
000040ec  __TI_CINIT_Warm               
000040c0  __TI_Handler_Table_Base       
000040cc  __TI_Handler_Table_Limit      
0000311d  __TI_auto_init_nobinit_nopinit
000029b9  __TI_decompress_lzss          
000033d7  __TI_decompress_none          
00002de5  __TI_ltoa                     
ffffffff  __TI_pprof_out_hndl           
000000c1  __TI_printfi                  
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
000033f9  __TI_zero_init                
00001f97  __adddf3                      
00003f00  __aeabi_ctype_table_          
00003f00  __aeabi_ctype_table_C         
00002ab1  __aeabi_d2f                   
00002f89  __aeabi_d2iz                  
00001f97  __aeabi_dadd                  
00002cc5  __aeabi_dcmpeq                
00002d01  __aeabi_dcmpge                
00002d15  __aeabi_dcmpgt                
00002ced  __aeabi_dcmple                
00002cd9  __aeabi_dcmplt                
0000237d  __aeabi_ddiv                  
0000258d  __aeabi_dmul                  
00001f8d  __aeabi_dsub                  
202004a0  __aeabi_errno                 
00003475  __aeabi_errno_addr            
0000309d  __aeabi_f2d                   
00003229  __aeabi_i2d                   
00002e95  __aeabi_idiv                  
00002c5f  __aeabi_idiv0                 
00002e95  __aeabi_idivmod               
00002fd3  __aeabi_ldiv0                 
00003365  __aeabi_llsl                  
0000331d  __aeabi_lmul                  
00003425  __aeabi_memclr                
00003425  __aeabi_memclr4               
00003425  __aeabi_memclr8               
0000347d  __aeabi_memcpy                
0000347d  __aeabi_memcpy4               
0000347d  __aeabi_memcpy8               
00003409  __aeabi_memset                
00003409  __aeabi_memset4               
00003409  __aeabi_memset8               
0000305d  __aeabi_uidiv                 
0000305d  __aeabi_uidivmod              
0000339d  __aeabi_uldivmod              
00003365  __ashldi3                     
ffffffff  __binit__                     
00002b91  __cmpdf2                      
0000237d  __divdf3                      
00002b91  __eqdf2                       
0000309d  __extendsfdf2                 
00002f89  __fixdfsi                     
00003229  __floatsidf                   
00002a35  __gedf2                       
00002a35  __gtdf2                       
00002b91  __ledf2                       
00002b91  __ltdf2                       
UNDEFED   __mpu_init                    
0000258d  __muldf3                      
0000331d  __muldi3                      
00003159  __muldsi3                     
00002b91  __nedf2                       
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00001f8d  __subdf3                      
00002ab1  __truncdfsf2                  
000027f1  __udivmoddi4                  
000032d1  _c_int00_noargs               
UNDEFED   _system_post_cinit            
0000348f  _system_pre_init              
00003485  abort                         
20200474  accX                          
20200480  accY                          
20200484  accZ                          
20200488  angleX                        
2020048c  angleY                        
20200490  angleZ                        
000030dd  atoi                          
ffffffff  binit                         
00003431  delay_init                    
00002749  delay_ms                      
00002d89  frexp                         
00002d89  frexpl                        
20200444  gUART_JY901SBackup            
20200494  gyroX                         
20200498  gyroY                         
2020049c  gyroZ                         
00000000  interruptVectors              
00002671  ldexp                         
00002671  ldexpl                        
00000a91  main                          
00003341  memccpy                       
00002893  memcpy                        
00002d27  memset                        
20200400  rxData                        
00002671  scalbn                        
00002671  scalbnl                       
00002931  strcmp                        
00003255  vsprintf                      
000033e9  wcslen                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  __TI_printfi                  
00000200  __STACK_SIZE                  
00000a91  main                          
00000e6d  OLED_WriteData                
00001209  OLED_WriteCommand             
00001581  OLED_ShowImage                
00001a05  OLED_ShowString               
00001dd1  OLED_Init                     
00001f8d  __aeabi_dsub                  
00001f8d  __subdf3                      
00001f97  __adddf3                      
00001f97  __aeabi_dadd                  
0000211f  ADC0_IRQHandler               
0000211f  ADC1_IRQHandler               
0000211f  AES_IRQHandler                
0000211f  CANFD0_IRQHandler             
0000211f  DAC0_IRQHandler               
0000211f  DMA_IRQHandler                
0000211f  Default_Handler               
0000211f  GROUP0_IRQHandler             
0000211f  GROUP1_IRQHandler             
0000211f  HardFault_Handler             
0000211f  I2C0_IRQHandler               
0000211f  I2C1_IRQHandler               
0000211f  NMI_Handler                   
0000211f  PendSV_Handler                
0000211f  RTC_IRQHandler                
0000211f  SPI0_IRQHandler               
0000211f  SPI1_IRQHandler               
0000211f  SVC_Handler                   
0000211f  SysTick_Handler               
0000211f  TIMA0_IRQHandler              
0000211f  TIMA1_IRQHandler              
0000211f  TIMG0_IRQHandler              
0000211f  TIMG12_IRQHandler             
0000211f  TIMG6_IRQHandler              
0000211f  TIMG7_IRQHandler              
0000211f  TIMG8_IRQHandler              
0000211f  UART0_IRQHandler              
0000211f  UART1_IRQHandler              
0000211f  UART2_IRQHandler              
0000211f  UART3_IRQHandler              
0000237d  __aeabi_ddiv                  
0000237d  __divdf3                      
00002489  OLED_Update                   
0000258d  __aeabi_dmul                  
0000258d  __muldf3                      
00002671  ldexp                         
00002671  ldexpl                        
00002671  scalbn                        
00002671  scalbnl                       
00002749  delay_ms                      
000027f1  __udivmoddi4                  
00002893  memcpy                        
0000292c  C$$EXIT                       
0000292d  HOSTexit                      
00002931  strcmp                        
000029b9  __TI_decompress_lzss          
00002a35  __gedf2                       
00002a35  __gtdf2                       
00002aa9  SYSCFG_DL_DMA_init            
00002ab1  __aeabi_d2f                   
00002ab1  __truncdfsf2                  
00002b25  SYSCFG_DL_UART_JY901S_init    
00002b91  __cmpdf2                      
00002b91  __eqdf2                       
00002b91  __ledf2                       
00002b91  __ltdf2                       
00002b91  __nedf2                       
00002c5f  __aeabi_idiv0                 
00002c61  OLED_ClearArea                
00002cc5  __aeabi_dcmpeq                
00002cd9  __aeabi_dcmplt                
00002ced  __aeabi_dcmple                
00002d01  __aeabi_dcmpge                
00002d15  __aeabi_dcmpgt                
00002d27  memset                        
00002d89  frexp                         
00002d89  frexpl                        
00002de5  __TI_ltoa                     
00002e95  __aeabi_idiv                  
00002e95  __aeabi_idivmod               
00002f3d  DL_DMA_initChannel            
00002f89  __aeabi_d2iz                  
00002f89  __fixdfsi                     
00002fd3  __aeabi_ldiv0                 
00002fd5  DL_UART_init                  
0000301d  SYSCFG_DL_SYSCTL_init         
0000305d  __aeabi_uidiv                 
0000305d  __aeabi_uidivmod              
0000309d  __aeabi_f2d                   
0000309d  __extendsfdf2                 
000030dd  atoi                          
0000311d  __TI_auto_init_nobinit_nopinit
00003159  __muldsi3                     
00003195  SYSCFG_DL_initPower           
000031c9  SYSCFG_DL_DMA_UART_JY901S_init
00003229  __aeabi_i2d                   
00003229  __floatsidf                   
00003255  vsprintf                      
00003281  OLED_Printf                   
000032a9  SYSCFG_DL_GPIO_init           
000032d1  _c_int00_noargs               
000032f9  SYSCFG_DL_init                
0000331d  __aeabi_lmul                  
0000331d  __muldi3                      
00003341  memccpy                       
00003365  __aeabi_llsl                  
00003365  __ashldi3                     
0000339d  __aeabi_uldivmod              
000033c5  DL_UART_setClockConfig        
000033d7  __TI_decompress_none          
000033e9  wcslen                        
000033f9  __TI_zero_init                
00003409  __aeabi_memset                
00003409  __aeabi_memset4               
00003409  __aeabi_memset8               
00003425  __aeabi_memclr                
00003425  __aeabi_memclr4               
00003425  __aeabi_memclr8               
00003431  delay_init                    
0000343d  DL_Common_delayCycles         
00003475  __aeabi_errno_addr            
0000347d  __aeabi_memcpy                
0000347d  __aeabi_memcpy4               
0000347d  __aeabi_memcpy8               
00003485  abort                         
0000348b  Reset_Handler                 
0000348f  _system_pre_init              
000034a0  OLED_F8x16                    
00003a90  OLED_F6x8                     
00003cca  OLED_CF16x16                  
00003f00  __aeabi_ctype_table_          
00003f00  __aeabi_ctype_table_C         
000040c0  __TI_Handler_Table_Base       
000040cc  __TI_Handler_Table_Limit      
000040dc  __TI_CINIT_Base               
000040ec  __TI_CINIT_Limit              
000040ec  __TI_CINIT_Warm               
20200000  OLED_DisplayBuf               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200400  rxData                        
20200444  gUART_JY901SBackup            
20200474  accX                          
20200478  SYSTEMCLOCK                   
20200480  accY                          
20200484  accZ                          
20200488  angleX                        
2020048c  angleY                        
20200490  angleZ                        
20200494  gyroX                         
20200498  gyroY                         
2020049c  gyroZ                         
202004a0  __aeabi_errno                 
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[177 symbols]
