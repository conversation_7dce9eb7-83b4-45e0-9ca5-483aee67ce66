/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --package "LQFP-48(PT)" --part "Default" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.20.0+3587"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO          = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1         = GPIO.addInstance();
const SYSCTL        = scripting.addModule("/ti/driverlib/SYSCTL");
const UART          = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1         = UART.addInstance();
const ProjectConfig = scripting.addModule("/ti/project_config/ProjectConfig");

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                              = "GPIO_OLED";
GPIO1.associatedPins.create(2);
GPIO1.associatedPins[0].initialValue     = "SET";
GPIO1.associatedPins[0].internalResistor = "PULL_UP";
GPIO1.associatedPins[0].assignedPort     = "PORTB";
GPIO1.associatedPins[0].assignedPin      = "6";
GPIO1.associatedPins[0].$name            = "SCL";
GPIO1.associatedPins[1].initialValue     = "SET";
GPIO1.associatedPins[1].internalResistor = "PULL_UP";
GPIO1.associatedPins[1].assignedPort     = "PORTB";
GPIO1.associatedPins[1].assignedPin      = "7";
GPIO1.associatedPins[1].$name            = "SDA";

const Board                       = scripting.addModule("/ti/driverlib/Board", {}, false);
Board.peripheral.$assign          = "DEBUGSS";
Board.peripheral.swclkPin.$assign = "PA20";
Board.peripheral.swdioPin.$assign = "PA19";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.peripheral.$assign    = "SYSCTL";

UART1.$name                                = "UART_JY901S";
UART1.targetBaudRate                       = 115200;
UART1.direction                            = "RX";
UART1.enabledDMARXTriggers                 = "DL_UART_DMA_INTERRUPT_RX";
UART1.peripheral.$assign                   = "UART3";
UART1.peripheral.rxPin.$assign             = "PA13";
UART1.rxPinConfig.$name                    = "ti_driverlib_gpio_GPIOPinGeneric1";
UART1.rxPinConfig.hideOutputInversion      = scripting.forceWrite(false);
UART1.rxPinConfig.onlyInternalResistor     = scripting.forceWrite(false);
UART1.rxPinConfig.passedPeripheralType     = scripting.forceWrite("Digital");
UART1.rxPinConfig.enableConfig             = true;
UART1.rxPinConfig.internalResistor         = "PULL_UP";
UART1.DMA_CHANNEL_RX.$name                 = "DMA_UART_JY901S";
UART1.DMA_CHANNEL_RX.addressMode           = "f2b";
UART1.DMA_CHANNEL_RX.srcLength             = "BYTE";
UART1.DMA_CHANNEL_RX.dstLength             = "BYTE";
UART1.DMA_CHANNEL_RX.configureTransferSize = true;
UART1.DMA_CHANNEL_RX.transferSize          = 66;
UART1.DMA_CHANNEL_RX.transferMode          = "FULL_CH_REPEAT_SINGLE";
UART1.DMA_CHANNEL_RX.destIncrement         = "INCREMENT";
UART1.DMA_CHANNEL_RX.peripheral.$assign    = "DMA_CH0";

ProjectConfig.deviceSpin = "MSPM0G3507";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
GPIO1.associatedPins[0].pin.$suggestSolution = "PB6";
GPIO1.associatedPins[1].pin.$suggestSolution = "PB7";
