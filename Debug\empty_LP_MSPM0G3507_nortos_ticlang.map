******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jun 18 13:26:39 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000329d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004100  0001bf00  R  X
  SRAM                  20200000   00008000  000006a0  00007960  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004100   00004100    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000033a0   000033a0    r-x .text
  00003460    00003460    00000c70   00000c70    r-- .rodata
  000040d0    000040d0    00000030   00000030    r-- .cinit
20200000    20200000    000004a0   00000000    rw-
  20200000    20200000    0000049c   00000000    rw- .bss
  2020049c    2020049c    00000004   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000033a0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000418     main.o (.text.main)
                  00000ea8    0000039c     OLED.o (.text.OLED_WriteData)
                  00001244    00000378     OLED.o (.text.OLED_WriteCommand)
                  000015bc    00000264     OLED.o (.text.OLED_ShowImage)
                  00001820    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001a40    000001f0     OLED.o (.text.OLED_ShowString)
                  00001c30    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001e0c    000001bc     OLED.o (.text.OLED_Init)
                  00001fc8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000215a    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000215c    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002298    00000120            : _printfi.c.obj (.text._pconv_e)
                  000023b8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000024c4    00000104     OLED.o (.text.OLED_Update)
                  000025c8    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000026ac    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002784    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002826    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  000028c0    00000094     delay.o (.text.delay_ms)
                  00002954    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00002960    00000088     libc.a : strcmp-armv6m.S.obj (.text:strcmp)
                  000029e8    0000007c            : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002a64    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002ad8    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00002ae0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002b54    0000006c     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_JY901S_init)
                  00002bc0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002c28    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002c8e    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002c90    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002cf2    00000062     libc.a : memset16.S.obj (.text:memset)
                  00002d54    0000005c            : s_frexp.c.obj (.text.frexp)
                  00002db0    00000058            : _ltoa.c.obj (.text.__TI_ltoa)
                  00002e08    00000058            : _printfi.c.obj (.text._pconv_f)
                  00002e60    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00002eb6    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00002f08    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00002f54    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002f9e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002fa0    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002fe8    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003028    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003068    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000030a8    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000030e8    0000003c            : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003124    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  0000315e    00000002     --HOLE-- [fill = 0]
                  00003160    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003194    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_UART_JY901S_init)
                  000031c4    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000031f4    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003220    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  0000324c    00000028     OLED.o (.text.OLED_Printf)
                  00003274    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000329c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000032c4    00000024     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000032e8    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  0000330c    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  0000332e    00000002     --HOLE-- [fill = 0]
                  00003330    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000334e    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00003366    00000002     --HOLE-- [fill = 0]
                  00003368    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  0000337c    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00003390    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000033a2    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  000033b4    00000010            : wcslen.c.obj (.text.wcslen)
                  000033c4    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  000033d4    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000033e2    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000033f0    0000000c     delay.o (.text.delay_init)
                  000033fc    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003406    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003410    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00003420    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000342a    0000000a            : vsprintf.c.obj (.text._outc)
                  00003434    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0000343c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003444    00000006     libc.a : exit.c.obj (.text:abort)
                  0000344a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000344e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00003452    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00003456    0000000a     --HOLE-- [fill = 0]

.cinit     0    000040d0    00000030     
                  000040d0    0000000c     (__TI_handler_table)
                  000040dc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000040e4    00000007     (.cinit..data.load) [load image, compression = lzss]
                  000040eb    00000001     --HOLE-- [fill = 0]
                  000040ec    00000010     (__TI_cinit_table)
                  000040fc    00000004     --HOLE-- [fill = 0]

.rodata    0    00003460    00000c70     
                  00003460    000005f0     OLED_Data.o (.rodata.OLED_F8x16)
                  00003a50    0000023a     OLED_Data.o (.rodata.OLED_F6x8)
                  00003c8a    0000022b     OLED_Data.o (.rodata.OLED_CF16x16)
                  00003eb5    00000001     --HOLE-- [fill = 0]
                  00003eb6    0000000a     ti_msp_dl_config.o (.rodata.gUART_JY901SConfig)
                  00003ec0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00003fc1    00000002     ti_msp_dl_config.o (.rodata.gUART_JY901SClockConfig)
                  00003fc3    00000001     --HOLE-- [fill = 0]
                  00003fc4    00000018     ti_msp_dl_config.o (.rodata.gDMA_UART_JY901SConfig)
                  00003fdc    00000016     main.o (.rodata.str1.15159059442110792349.1)
                  00003ff2    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00004003    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00004014    0000000f     main.o (.rodata.str1.12180500075719773345.1)
                  00004023    0000000f     main.o (.rodata.str1.17100691992556644108.1)
                  00004032    0000000f     main.o (.rodata.str1.18227636981041470289.1)
                  00004041    0000000f     main.o (.rodata.str1.2846389346932560359.1)
                  00004050    0000000f     main.o (.rodata.str1.288608917962107522.1)
                  0000405f    0000000f     main.o (.rodata.str1.8154729771448623357.1)
                  0000406e    0000000e     main.o (.rodata.str1.14112174005742061988.1)
                  0000407c    0000000e     main.o (.rodata.str1.14402440632258215044.1)
                  0000408a    0000000e     main.o (.rodata.str1.15996357275703397564.1)
                  00004098    0000000e     main.o (.rodata.str1.16265332423659789559.1)
                  000040a6    0000000e     main.o (.rodata.str1.2123964117280943167.1)
                  000040b4    0000000e     main.o (.rodata.str1.7702501944640899365.1)
                  000040c2    0000000e     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000049c     UNINITIALIZED
                  20200000    00000400     (.common:OLED_DisplayBuf)
                  20200400    00000042     (.common:rxData)
                  20200442    00000002     --HOLE--
                  20200444    00000030     (.common:gUART_JY901SBackup)
                  20200474    00000004     (.common:SYSTEMCLOCK)
                  20200478    00000004     (.common:accX)
                  2020047c    00000004     (.common:accY)
                  20200480    00000004     (.common:accZ)
                  20200484    00000004     (.common:angleX)
                  20200488    00000004     (.common:angleY)
                  2020048c    00000004     (.common:angleZ)
                  20200490    00000004     (.common:gyroX)
                  20200494    00000004     (.common:gyroY)
                  20200498    00000004     (.common:gyroZ)

.data      0    2020049c    00000004     UNINITIALIZED
                  2020049c    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       main.o                         1048    196       102    
       ti_msp_dl_config.o             356     36        48     
       startup_mspm0g350x_ticlang.o   6       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1410    424       150    
                                                               
    .\hardware\OLED\
       OLED.o                         3664    0         1024   
       OLED_Data.o                    0       2645      0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3664    2645      1024   
                                                               
    .\hardware\delay\
       delay.o                        160     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         160     0         4      
                                                               
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         176     0         0      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       memcpy16.S.obj                 154     0         0      
       strcmp-armv6m.S.obj            136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5762    291       4      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       udivmoddi4.S.obj               162     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       muldsi3.S.obj                  58      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2024    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       43        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   13200   3403      1694   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000040ec records: 2, size/record: 8, table size: 16
	.bss: load addr=000040dc, load size=00000008 bytes, run addr=20200000, run size=0000049c bytes, compression=zero_init
	.data: load addr=000040e4, load size=00000007 bytes, run addr=2020049c, run size=00000004 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000040d0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001fc9     00003410     0000340e   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000215b  ADC0_IRQHandler               
0000215b  ADC1_IRQHandler               
0000215b  AES_IRQHandler                
0000344a  C$$EXIT                       
0000215b  CANFD0_IRQHandler             
0000215b  DAC0_IRQHandler               
000033fd  DL_Common_delayCycles         
00002f09  DL_DMA_initChannel            
00002fa1  DL_UART_init                  
00003391  DL_UART_setClockConfig        
0000215b  DMA_IRQHandler                
0000215b  Default_Handler               
0000215b  GROUP0_IRQHandler             
0000215b  GROUP1_IRQHandler             
0000344b  HOSTexit                      
0000215b  HardFault_Handler             
0000215b  I2C0_IRQHandler               
0000215b  I2C1_IRQHandler               
0000215b  NMI_Handler                   
00003c8a  OLED_CF16x16                  
20200000  OLED_DisplayBuf               
00003a50  OLED_F6x8                     
00003460  OLED_F8x16                    
00001e0d  OLED_Init                     
0000324d  OLED_Printf                   
000015bd  OLED_ShowImage                
00001a41  OLED_ShowString               
000024c5  OLED_Update                   
00001245  OLED_WriteCommand             
00000ea9  OLED_WriteData                
0000215b  PendSV_Handler                
0000215b  RTC_IRQHandler                
0000344f  Reset_Handler                 
0000215b  SPI0_IRQHandler               
0000215b  SPI1_IRQHandler               
0000215b  SVC_Handler                   
00003195  SYSCFG_DL_DMA_UART_JY901S_init
00002ad9  SYSCFG_DL_DMA_init            
00003275  SYSCFG_DL_GPIO_init           
00002fe9  SYSCFG_DL_SYSCTL_init         
00002b55  SYSCFG_DL_UART_JY901S_init    
000032c5  SYSCFG_DL_init                
00003161  SYSCFG_DL_initPower           
20200474  SYSTEMCLOCK                   
0000215b  SysTick_Handler               
0000215b  TIMA0_IRQHandler              
0000215b  TIMA1_IRQHandler              
0000215b  TIMG0_IRQHandler              
0000215b  TIMG12_IRQHandler             
0000215b  TIMG6_IRQHandler              
0000215b  TIMG7_IRQHandler              
0000215b  TIMG8_IRQHandler              
0000215b  UART0_IRQHandler              
0000215b  UART1_IRQHandler              
0000215b  UART2_IRQHandler              
0000215b  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
000040ec  __TI_CINIT_Base               
000040fc  __TI_CINIT_Limit              
000040fc  __TI_CINIT_Warm               
000040d0  __TI_Handler_Table_Base       
000040dc  __TI_Handler_Table_Limit      
000030e9  __TI_auto_init_nobinit_nopinit
000029e9  __TI_decompress_lzss          
000033a3  __TI_decompress_none          
00002db1  __TI_ltoa                     
ffffffff  __TI_pprof_out_hndl           
000000c1  __TI_printfi                  
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
000033c5  __TI_zero_init                
00001fd3  __adddf3                      
00003ec0  __aeabi_ctype_table_          
00003ec0  __aeabi_ctype_table_C         
00002ae1  __aeabi_d2f                   
00002f55  __aeabi_d2iz                  
00001fd3  __aeabi_dadd                  
00002c91  __aeabi_dcmpeq                
00002ccd  __aeabi_dcmpge                
00002ce1  __aeabi_dcmpgt                
00002cb9  __aeabi_dcmple                
00002ca5  __aeabi_dcmplt                
000023b9  __aeabi_ddiv                  
000025c9  __aeabi_dmul                  
00001fc9  __aeabi_dsub                  
2020049c  __aeabi_errno                 
00003435  __aeabi_errno_addr            
00003069  __aeabi_f2d                   
000031f5  __aeabi_i2d                   
00002e61  __aeabi_idiv                  
00002c8f  __aeabi_idiv0                 
00002e61  __aeabi_idivmod               
00002f9f  __aeabi_ldiv0                 
00003331  __aeabi_llsl                  
000032e9  __aeabi_lmul                  
00002955  __aeabi_memclr                
00002955  __aeabi_memclr4               
00002955  __aeabi_memclr8               
0000343d  __aeabi_memcpy                
0000343d  __aeabi_memcpy4               
0000343d  __aeabi_memcpy8               
000033d5  __aeabi_memset                
000033d5  __aeabi_memset4               
000033d5  __aeabi_memset8               
00003029  __aeabi_uidiv                 
00003029  __aeabi_uidivmod              
00003369  __aeabi_uldivmod              
00003331  __ashldi3                     
ffffffff  __binit__                     
00002bc1  __cmpdf2                      
000023b9  __divdf3                      
00002bc1  __eqdf2                       
00003069  __extendsfdf2                 
00002f55  __fixdfsi                     
000031f5  __floatsidf                   
00002a65  __gedf2                       
00002a65  __gtdf2                       
00002bc1  __ledf2                       
00002bc1  __ltdf2                       
UNDEFED   __mpu_init                    
000025c9  __muldf3                      
000032e9  __muldi3                      
00003125  __muldsi3                     
00002bc1  __nedf2                       
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00001fc9  __subdf3                      
00002ae1  __truncdfsf2                  
00002785  __udivmoddi4                  
0000329d  _c_int00_noargs               
UNDEFED   _system_post_cinit            
00003453  _system_pre_init              
00003445  abort                         
20200478  accX                          
2020047c  accY                          
20200480  accZ                          
20200484  angleX                        
20200488  angleY                        
2020048c  angleZ                        
000030a9  atoi                          
ffffffff  binit                         
000033f1  delay_init                    
000028c1  delay_ms                      
00002d55  frexp                         
00002d55  frexpl                        
20200444  gUART_JY901SBackup            
20200490  gyroX                         
20200494  gyroY                         
20200498  gyroZ                         
00000000  interruptVectors              
000026ad  ldexp                         
000026ad  ldexpl                        
00000a91  main                          
0000330d  memccpy                       
00002827  memcpy                        
00002cf3  memset                        
20200400  rxData                        
000026ad  scalbn                        
000026ad  scalbnl                       
00002961  strcmp                        
00003221  vsprintf                      
000033b5  wcslen                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  __TI_printfi                  
00000200  __STACK_SIZE                  
00000a91  main                          
00000ea9  OLED_WriteData                
00001245  OLED_WriteCommand             
000015bd  OLED_ShowImage                
00001a41  OLED_ShowString               
00001e0d  OLED_Init                     
00001fc9  __aeabi_dsub                  
00001fc9  __subdf3                      
00001fd3  __adddf3                      
00001fd3  __aeabi_dadd                  
0000215b  ADC0_IRQHandler               
0000215b  ADC1_IRQHandler               
0000215b  AES_IRQHandler                
0000215b  CANFD0_IRQHandler             
0000215b  DAC0_IRQHandler               
0000215b  DMA_IRQHandler                
0000215b  Default_Handler               
0000215b  GROUP0_IRQHandler             
0000215b  GROUP1_IRQHandler             
0000215b  HardFault_Handler             
0000215b  I2C0_IRQHandler               
0000215b  I2C1_IRQHandler               
0000215b  NMI_Handler                   
0000215b  PendSV_Handler                
0000215b  RTC_IRQHandler                
0000215b  SPI0_IRQHandler               
0000215b  SPI1_IRQHandler               
0000215b  SVC_Handler                   
0000215b  SysTick_Handler               
0000215b  TIMA0_IRQHandler              
0000215b  TIMA1_IRQHandler              
0000215b  TIMG0_IRQHandler              
0000215b  TIMG12_IRQHandler             
0000215b  TIMG6_IRQHandler              
0000215b  TIMG7_IRQHandler              
0000215b  TIMG8_IRQHandler              
0000215b  UART0_IRQHandler              
0000215b  UART1_IRQHandler              
0000215b  UART2_IRQHandler              
0000215b  UART3_IRQHandler              
000023b9  __aeabi_ddiv                  
000023b9  __divdf3                      
000024c5  OLED_Update                   
000025c9  __aeabi_dmul                  
000025c9  __muldf3                      
000026ad  ldexp                         
000026ad  ldexpl                        
000026ad  scalbn                        
000026ad  scalbnl                       
00002785  __udivmoddi4                  
00002827  memcpy                        
000028c1  delay_ms                      
00002955  __aeabi_memclr                
00002955  __aeabi_memclr4               
00002955  __aeabi_memclr8               
00002961  strcmp                        
000029e9  __TI_decompress_lzss          
00002a65  __gedf2                       
00002a65  __gtdf2                       
00002ad9  SYSCFG_DL_DMA_init            
00002ae1  __aeabi_d2f                   
00002ae1  __truncdfsf2                  
00002b55  SYSCFG_DL_UART_JY901S_init    
00002bc1  __cmpdf2                      
00002bc1  __eqdf2                       
00002bc1  __ledf2                       
00002bc1  __ltdf2                       
00002bc1  __nedf2                       
00002c8f  __aeabi_idiv0                 
00002c91  __aeabi_dcmpeq                
00002ca5  __aeabi_dcmplt                
00002cb9  __aeabi_dcmple                
00002ccd  __aeabi_dcmpge                
00002ce1  __aeabi_dcmpgt                
00002cf3  memset                        
00002d55  frexp                         
00002d55  frexpl                        
00002db1  __TI_ltoa                     
00002e61  __aeabi_idiv                  
00002e61  __aeabi_idivmod               
00002f09  DL_DMA_initChannel            
00002f55  __aeabi_d2iz                  
00002f55  __fixdfsi                     
00002f9f  __aeabi_ldiv0                 
00002fa1  DL_UART_init                  
00002fe9  SYSCFG_DL_SYSCTL_init         
00003029  __aeabi_uidiv                 
00003029  __aeabi_uidivmod              
00003069  __aeabi_f2d                   
00003069  __extendsfdf2                 
000030a9  atoi                          
000030e9  __TI_auto_init_nobinit_nopinit
00003125  __muldsi3                     
00003161  SYSCFG_DL_initPower           
00003195  SYSCFG_DL_DMA_UART_JY901S_init
000031f5  __aeabi_i2d                   
000031f5  __floatsidf                   
00003221  vsprintf                      
0000324d  OLED_Printf                   
00003275  SYSCFG_DL_GPIO_init           
0000329d  _c_int00_noargs               
000032c5  SYSCFG_DL_init                
000032e9  __aeabi_lmul                  
000032e9  __muldi3                      
0000330d  memccpy                       
00003331  __aeabi_llsl                  
00003331  __ashldi3                     
00003369  __aeabi_uldivmod              
00003391  DL_UART_setClockConfig        
000033a3  __TI_decompress_none          
000033b5  wcslen                        
000033c5  __TI_zero_init                
000033d5  __aeabi_memset                
000033d5  __aeabi_memset4               
000033d5  __aeabi_memset8               
000033f1  delay_init                    
000033fd  DL_Common_delayCycles         
00003435  __aeabi_errno_addr            
0000343d  __aeabi_memcpy                
0000343d  __aeabi_memcpy4               
0000343d  __aeabi_memcpy8               
00003445  abort                         
0000344a  C$$EXIT                       
0000344b  HOSTexit                      
0000344f  Reset_Handler                 
00003453  _system_pre_init              
00003460  OLED_F8x16                    
00003a50  OLED_F6x8                     
00003c8a  OLED_CF16x16                  
00003ec0  __aeabi_ctype_table_          
00003ec0  __aeabi_ctype_table_C         
000040d0  __TI_Handler_Table_Base       
000040dc  __TI_Handler_Table_Limit      
000040ec  __TI_CINIT_Base               
000040fc  __TI_CINIT_Limit              
000040fc  __TI_CINIT_Warm               
20200000  OLED_DisplayBuf               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200400  rxData                        
20200444  gUART_JY901SBackup            
20200474  SYSTEMCLOCK                   
20200478  accX                          
2020047c  accY                          
20200480  accZ                          
20200484  angleX                        
20200488  angleY                        
2020048c  angleZ                        
20200490  gyroX                         
20200494  gyroY                         
20200498  gyroZ                         
2020049c  __aeabi_errno                 
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[176 symbols]
